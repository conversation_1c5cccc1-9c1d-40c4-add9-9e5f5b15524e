import sys
import os
import ctypes
from ctypes import wintypes
import io
import time
import traceback

# --- 调试开关 ---
DEBUG_BLACKLIST = True
DEBUG_CACHE = True

# --- 黑名单配置 ---
BLACKLIST_KEYWORDS = [
    "广告", "游戏", "抽奖", "lottery", "gambling",
]

# --- 1. 初始化和加载 ---
# (此部分与原代码完全相同，保持不变)
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', line_buffering=True)
try:
    # 兼容PyInstaller打包后的路径
    script_dir = os.path.dirname(sys.executable) if getattr(sys, 'frozen', False) else os.path.dirname(os.path.realpath(__file__))
    uiacomwrapper_path = os.path.join(script_dir, "UIAComWrapper.********/lib/net40")
    if not os.path.exists(uiacomwrapper_path):
        print(f"致命错误: 无法找到UIA依赖路径 {uiacomwrapper_path}")
        input("按 Enter 键退出...")
        sys.exit(1)
    sys.path.append(uiacomwrapper_path)
    import clr
    clr.AddReference("UIAComWrapper")
    clr.AddReference("Interop.UIAutomationClient")
    from System.Windows.Automation import AutomationElement, TreeScope, PropertyCondition, OrCondition, AndCondition, TreeWalker, PropertyConditionFlags
    from System.Windows.Automation import ControlType, ValuePattern, LegacyIAccessiblePattern, TextPattern, InvokePattern, SelectionItemPattern
    from System.Diagnostics import Process
    from System import IntPtr, Exception as DotNetException
    UIA_LOADED = True
    print("UIAComWrapper 加载成功!")
except Exception as e:
    UIA_LOADED = False
    print(f"警告: UIAComWrapper 加载失败: {e}")
    traceback.print_exc()

user32 = ctypes.windll.user32

# --- 2. 辅助函数 (部分已修改) ---
def get_window_process_name_and_title(hwnd):
    process_name, window_title = "", ""
    try:
        process_id = wintypes.DWORD()
        user32.GetWindowThreadProcessId(hwnd, ctypes.byref(process_id))
        if UIA_LOADED:
            process = Process.GetProcessById(process_id.value)
            process_name = process.ProcessName.lower()
        length = user32.GetWindowTextLengthW(hwnd)
        if length > 0:
            buffer = ctypes.create_unicode_buffer(length + 1)
            user32.GetWindowTextW(hwnd, buffer, length + 1)
            window_title = buffer.value
    except Exception:
        pass
    return process_name, window_title

def _is_valid_url(url_text):
    if not url_text or len(url_text) < 5: return False
    url_text = url_text.strip().lower()
    return url_text.startswith(('http:', 'https:', 'ftp:', 'file:')) or \
           ('.com' in url_text or '.cn' in url_text or '.org' in url_text or 'about:' in url_text)

def _extract_url_from_element(element):
    if not element: return None
    try:
        value_pattern = element.GetCurrentPattern(ValuePattern.Pattern)
        if value_pattern and _is_valid_url(value_pattern.Current.Value): return value_pattern.Current.Value.strip()
        legacy_pattern = element.GetCurrentPattern(LegacyIAccessiblePattern.Pattern)
        if legacy_pattern and _is_valid_url(legacy_pattern.Current.Value): return legacy_pattern.Current.Value.strip()
        text_pattern = element.GetCurrentPattern(TextPattern.Pattern)
        if text_pattern:
            url = text_pattern.DocumentRange.GetText(-1)
            if _is_valid_url(url): return url.strip()
    except Exception:
        pass
    return None

# --- V5.2 修复: Firefox URL获取函数 ---
def _get_url_firefox(window_element):
    """
    获取Firefox浏览器URL和标题的最终健壮版。
    新核心策略：查找唯一的、对用户可见的文档控件。
    通过 AndCondition(ControlType=Document, IsOffscreen=False) 实现。
    这能准确命中当前活动标签页（包括新标签页）的内容窗格。
    """
    try:
        # 步骤 1: 创建一个复合条件，查找“类型为Document”且“在屏幕上可见”的控件
        doc_cond = PropertyCondition(AutomationElement.ControlTypeProperty, ControlType.Document)
        onscreen_cond = PropertyCondition(AutomationElement.IsOffscreenProperty, False)
        active_doc_cond = AndCondition(doc_cond, onscreen_cond)
        
        # 步骤 2: 使用此条件在整个窗口中查找唯一的活动文档窗格
        active_doc_element = window_element.FindFirst(TreeScope.Descendants, active_doc_cond)
        
        if active_doc_element:
            # 步骤 3: 从找到的活动文档中提取URL和标题
            url = _extract_url_from_element(active_doc_element)
            title = active_doc_element.Current.Name
            
            # 如果文档标题为空，则使用窗口标题作为备用
            final_title = title or window_element.Current.Name
            return url, final_title

    except Exception:
        # traceback.print_exc() # 调试时可取消注释
        pass
        
    # 作为最终的备用方案，如果上述所有UIA方法都失败了，
    # 至少返回窗口的标题，这样上层逻辑不会认为策略失败。
    return None, window_element.Current.Name

# --- V5.2 修复: Chromium URL获取函数 ---
def _get_url_chromium_generic(window_element):
    """
    Chromium通用策略。
    修复：即使找不到有效URL（如新标签页），也返回窗口标题，
    以便上层调度器判定为“识别成功”，从而避免不必要的策略重试。
    """
    try:
        or_cond = OrCondition(
            PropertyCondition(AutomationElement.ControlTypeProperty, ControlType.Edit),
            PropertyCondition(AutomationElement.ControlTypeProperty, ControlType.ComboBox)
        )
        for elem in list(window_element.FindAll(TreeScope.Descendants, or_cond)):
            url = _extract_url_from_element(elem)
            if url:
                # 找到URL，返回URL和窗口标题
                return url, window_element.Current.Name
    except Exception:
        pass
    # 找不到任何有效URL，但仍返回窗口标题，表示已成功识别
    return None, window_element.Current.Name

# --- V5.2 修复: 带缓存的URL获取调度器 ---
URL_STRATEGIES = {
    'get_url_firefox': _get_url_firefox,
    'get_url_chromium_generic': _get_url_chromium_generic,
}

def get_browser_info_optimized(hwnd, process_name, cache):
    """
    V5.3 修复版：引入“结果质量”和“机会性升级”缓存机制。
    此版本解决了“次优策略缓存锁定”问题。它会优先尝试能同时获取URL和标题的
    “高质量”策略，即使已缓存的策略只能获取标题，它仍会尝试寻找更好的策略进行升级。
    """
    if not UIA_LOADED: return None, None, None, None
    try:
        window_element = AutomationElement.FromHandle(IntPtr(hwnd))
        if not window_element: return None, None, None, None

        # 准备要尝试的策略列表
        strategies_to_try = [('get_url_chromium_generic', _get_url_chromium_generic)]
        if "firefox" in process_name:
            strategies_to_try.insert(0, ('get_url_firefox', _get_url_firefox))

        # 检查缓存，如果存在，将缓存的策略移到列表最前面，优先尝试
        cached_strategy_name = cache.get(process_name)
        if cached_strategy_name:
            if DEBUG_CACHE: print(f"  [Cache] 命中URL获取缓存, 优先尝试策略: {cached_strategy_name}")
            # 找到缓存策略并移到最前
            cached_item = next((item for item in strategies_to_try if item[0] == cached_strategy_name), None)
            if cached_item:
                strategies_to_try.remove(cached_item)
                strategies_to_try.insert(0, cached_item)

        best_result_so_far = (None, None, "未找到URL", window_element)
        strategy_found = False

        # 遍历所有策略，寻找最佳结果
        for name, func in strategies_to_try:
            url, title = func(window_element)
            
            # 只要策略能找到标题，就认为它至少部分成功了
            if title:
                # 高质量结果：URL和标题都找到了！这是我们想要的，立即采纳并更新缓存。
                if url:
                    if DEBUG_CACHE and name != cached_strategy_name:
                        print(f"  [Cache] 发现更优策略 '{name}' (获取到URL). 更新缓存。")
                    cache[process_name] = name
                    return url, title, f"策略({name})", window_element
                
                # 低质量结果：只找到标题。先保存起来，但继续寻找更好的。
                # 我们只保存第一个找到的（低质量）结果，作为最后的备用选项。
                elif not strategy_found:
                    best_result_so_far = (url, title, f"策略({name})-仅标题", window_element)
                    strategy_found = True
        
        # 如果循环结束都没找到高质量结果，则返回我们找到的那个最好的（低质量）结果
        return best_result_so_far

    except Exception as e:
        return None, None, f"分析异常 {e}", None
        
    # 终极备用
    try:
        return None, window_element.Current.Name, "未找到URL", window_element
    except:
        return None, None, "未找到URL且窗口元素无效", None
# --- 3. 标签页关闭核心逻辑 (保持不变) ---
def check_title_against_blacklist(title, blacklist):
    if not title: return False
    title_lower = title.lower()
    for keyword in blacklist:
        if keyword.lower() in title_lower:
            if DEBUG_BLACKLIST: print(f"  [Debug] 匹配到黑名单关键字 '{keyword}' in title '{title}'")
            return True
    return False

CLOSE_BUTTON_CONDITION = AndCondition(
    PropertyCondition(AutomationElement.ControlTypeProperty, ControlType.Button),
    OrCondition(
        PropertyCondition(AutomationElement.NameProperty, "关闭", PropertyConditionFlags.IgnoreCase),
        PropertyCondition(AutomationElement.NameProperty, "Close", PropertyConditionFlags.IgnoreCase)
    )
)
TAB_ITEM_CONDITION = PropertyCondition(AutomationElement.ControlTypeProperty, ControlType.TabItem)

def _invoke_click(element):
    if not element: return False
    try:
        element.GetCurrentPattern(InvokePattern.Pattern).Invoke()
        return True
    except Exception:
        return False
        
def _strategy_indexed_match(active_tab, depth=None):
    walker = TreeWalker.ControlViewWalker
    ancestor = walker.GetParent(active_tab)
    def find_match_in_ancestor(scope, tab_to_match):
        all_tabs = list(scope.FindAll(TreeScope.Descendants, TAB_ITEM_CONDITION))
        all_buttons = list(scope.FindAll(TreeScope.Descendants, CLOSE_BUTTON_CONDITION))
        if DEBUG_BLACKLIST: print(f"    [探索] 在'{scope.Current.Name}'下找到 {len(all_tabs)} 个标签和 {len(all_buttons)} 个关闭按钮")
        if len(all_tabs) > 0 and len(all_tabs) == len(all_buttons):
            try:
                active_tab_hash = tab_to_match.GetHashCode()
                for i, tab in enumerate(all_tabs):
                    if tab.GetHashCode() == active_tab_hash: return all_buttons[i]
            except Exception: pass
        return None
    if depth is not None:
        for _ in range(depth - 1):
            if not ancestor: return None
            ancestor = walker.GetParent(ancestor)
        if ancestor:
            button = find_match_in_ancestor(ancestor, active_tab)
            if button: return {'button': button, 'depth': depth}
        return None
    for current_depth in range(1, 6):
        if not ancestor: break
        if DEBUG_BLACKLIST: print(f"  [探索] 向上追溯到第 {current_depth} 层父容器: '{ancestor.Current.Name}'")
        button = find_match_in_ancestor(ancestor, active_tab)
        if button: return {'button': button, 'depth': current_depth}
        ancestor = walker.GetParent(ancestor)
    return None

def _strategy_internal_button(active_tab, **kwargs):
    button = active_tab.FindFirst(TreeScope.Descendants, CLOSE_BUTTON_CONDITION)
    return {'button': button} if button else None

STRATEGIES_TO_TRY = [
    {'name': 'indexed_match', 'function': _strategy_indexed_match},
    {'name': 'internal_button', 'function': _strategy_internal_button},
]

def close_browser_tab(window_element, process_name, cache):
    try:
        active_tab = None
        all_tabs = list(window_element.FindAll(TreeScope.Descendants, TAB_ITEM_CONDITION))
        for tab in all_tabs:
            try:
                # 优先使用 SelectionItemPattern，这是最标准的方式
                if tab.GetCurrentPattern(SelectionItemPattern.Pattern).Current.IsSelected:
                    active_tab = tab
                    if DEBUG_BLACKLIST: print(f"  [Debug] 通过 IsSelected 找到活动标签页锚点: '{active_tab.Current.Name}'")
                    break
            except Exception: continue

        # 如果标准方法找不到，并且只有一个标签页，那么它就是活动标签页
        if not active_tab and len(all_tabs) == 1:
            active_tab = all_tabs[0]
            if DEBUG_BLACKLIST: print(f"  [Debug] 通过唯一标签页规则找到锚点: '{active_tab.Current.Name}'")

        if not active_tab:
            if DEBUG_BLACKLIST: print("  [Debug] 关键失败: 无法定位到活动标签页。")
            return None, False
        
        cached_strategy_info = cache.get(process_name)
        if cached_strategy_info:
            if DEBUG_CACHE: print(f"  [Cache] 命中关闭策略缓存，为'{process_name}'采用 '{cached_strategy_info['name']}' 策略...")
            strategy_func = globals().get(f"_strategy_{cached_strategy_info['name']}")
            if strategy_func:
                result = strategy_func(active_tab, **cached_strategy_info)
                if result and _invoke_click(result.get('button')):
                    if DEBUG_CACHE: print("  [Cache] 关闭策略缓存执行成功!")
                    return None, True
            if DEBUG_CACHE: print(f"  [Cache] 关闭策略缓存 '{cached_strategy_info['name']}' 已失效，清除并开始瀑布流探索...")
            cache.pop(process_name, None)
        
        if DEBUG_CACHE and not cached_strategy_info:
            print(f"  [Cache] 未找到'{process_name}'的关闭策略缓存, 开始瀑布流探索...")

        for strategy in STRATEGIES_TO_TRY:
            if DEBUG_BLACKLIST: print(f"  [发现] 尝试关闭策略: '{strategy['name']}' ...")
            result = strategy['function'](active_tab)
            if result and _invoke_click(result.get('button')):
                if DEBUG_BLACKLIST: print(f"  [发现] 关闭策略 '{strategy['name']}' 成功！")
                new_cache_entry = {'name': strategy['name']}
                if 'depth' in result: new_cache_entry['depth'] = result['depth']
                return new_cache_entry, True
    except DotNetException as e:
        if DEBUG_BLACKLIST: print(f"  [Debug] 关闭标签页时发生UIA异常: {e}")
    except Exception:
        if DEBUG_BLACKLIST: print(f"  [Debug] 关闭标签页时发生未知异常:")
        traceback.print_exc()
        
    if DEBUG_BLACKLIST: print("  [Debug] 所有关闭策略均已尝试，全部失败。")
    return None, False

# --- 4. 主监控逻辑 (保持不变) ---
def monitor_focused_window():
    if not UIA_LOADED:
        print("\nUIA组件加载失败，程序无法运行。")
        input("按Enter键退出...")
        return

    browser_processes = {"msedge", "chrome", "firefox", "opera", "brave", "vivaldi", "iexplore", "360se", "liebao"}
    
    last_hwnd = None
    last_process_name = None
    last_reported_state = (None, None, None)

    url_path_cache = {}
    tab_close_strategy_cache = {}

    print("\n" + "=" * 60)
    print("开始实时监控焦点窗口 (V5.2 - 稳定修复版)")
    print("核心思想: 浏览器实时探测，非浏览器轻量检查")
    print(f"当前已识别浏览器进程: {', '.join(sorted(list(browser_processes)))}")
    print(f"当前黑名单关键字: {', '.join(BLACKLIST_KEYWORDS)}")
    print("按 Ctrl+C 退出程序。")
    print("=" * 60 + "\n")

    try:
        while True:
            time.sleep(1.5)
            hwnd = user32.GetForegroundWindow()
            if not hwnd:
                if last_hwnd is not None:
                    last_hwnd, last_process_name, last_reported_state = None, None, (None, None, None)
                    print(f"--- STATE CHANGE at {time.strftime('%Y-%m-%d %H:%M:%S')} ---")
                    print("[系统] 暂无活动窗口")
                    print("-" * 35 + "\n")
                continue

            if hwnd == last_hwnd and last_process_name not in browser_processes:
                continue

            process_name, window_title = get_window_process_name_and_title(hwnd)
            if not process_name and not window_title and hwnd == last_hwnd:
                continue
            
            last_hwnd = hwnd
            last_process_name = process_name

            current_url, url_strategy, window_element = None, "N/A", None

            is_browser = process_name in browser_processes
            if is_browser:
                current_url, doc_title, url_strategy, window_element = get_browser_info_optimized(hwnd, process_name, url_path_cache)
                if doc_title:
                    window_title = doc_title

            if is_browser and window_element and check_title_against_blacklist(window_title, BLACKLIST_KEYWORDS):
                print(f"[{time.strftime('%H:%M:%S')}] 检测到黑名单标题: {window_title}")
                print(f"  来自进程: {process_name}")
                if current_url: print(f"  关联URL: {current_url}")
                print("  正在尝试关闭标签页...")
                
                newly_discovered_strategy, success = close_browser_tab(window_element, process_name, tab_close_strategy_cache)
                
                if success:
                    print("  成功关闭标签页！")
                    if newly_discovered_strategy:
                        tab_close_strategy_cache[process_name] = newly_discovered_strategy
                        if DEBUG_CACHE: 
                            print(f"  [Cache] 已为'{process_name}'创建/更新关闭策略缓存: {newly_discovered_strategy}")
                    
                    last_reported_state = (None, None, None) 
                    print("-" * 35 + "\n")
                    time.sleep(0.5)
                    continue 
                else:
                    print("  关闭标签页失败。")

            current_state = (hwnd, window_title, current_url)
            if current_state != last_reported_state:
                print(f"--- STATE CHANGE at {time.strftime('%Y-%m-%d %H:%M:%S')} ---")
                if is_browser:
                    print(f"[浏览器] {process_name}: {window_title}")
                    if current_url: print(f"  URL     : {current_url}")
                    if url_strategy: print(f"  获取方式: {url_strategy}")
                else:
                    print(f"[应  用] {process_name or '未知'}: {window_title}")
                print("-" * 35 + "\n")
                last_reported_state = current_state

    except KeyboardInterrupt:
        print("\n监控程序已停止。")
    except Exception as e:
        print(f"\n监控过程中发生未预料的错误: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    monitor_focused_window()